# Sample Authentication Credentials for TeamLogic-AutoTask System

## User Accounts (from USER_DUMMY_DATA table)
### Format: Email | Password | Name | Phone

<EMAIL> | user123| Anant Lad | **********
<EMAIL> | user456 | Vidhi <PERSON> | ******-0102
<EMAIL> | user789 | <PERSON><PERSON><PERSON> | ******-0103
<EMAIL> | user101 | Madhavi ghalame | ******-0104

## Technician Accounts (from TECHNICIAN_DUMMY_DATA table)
### Format: Email | Password | Name | Role | Technician ID

<EMAIL> | tech123 | Shu<PERSON><PERSON><PERSON>te | Email Specialist | TECH-001
<EMAIL> | tech456 | Vidhi Dave  | Hardware Technician | TECH-002
<EMAIL> | tech789 | <PERSON><PERSON><PERSON> | Network Administrator | TECH-003
<EMAIL> | tech101 | <PERSON><PERSON><PERSON> | Software Specialist | TECH-004

## Usage Instructions

### For Users:
1. Login with any user email and password from the User Accounts section
2. Access User Dashboard to:
   - Raise new tickets
   - Chat with assigned technicians
   - View ticket resolutions
   - Track ticket status (raised/in-progress/completed)

### For Technicians:
1. Login with any technician email and password from the Technician Accounts section
2. Access Technician Dashboard to:
   - View assigned tickets
   - Update ticket status and priority
   - Write notes and resolutions
   - Chat with users

### Test Scenarios:
1. <NAME_EMAIL> (user123) to test user features
2. <NAME_EMAIL> (tech123) to test technician features
3. Create tickets as a user and handle them as a technician
4. Test real-time chat between user and technician

## Database Tables Created:
- USER_DUMMY_DATA: User authentication and profile data
- TECHNICIAN_DUMMY_DATA: Technician authentication and profile data (updated with PASSWORD field)
- CHAT_MESSAGES: Real-time chat messages between users and technicians
- TICKET_UPDATES: Ticket status/priority changes and notes
- USER_SESSIONS: Active user sessions for authentication
- TICKETS: Main ticket storage (existing table)

## Security Notes:
- Passwords are stored in plain text for demo purposes only
- In production, use proper password hashing (bcrypt, etc.)
- Implement proper session management and timeout
- Add CSRF protection and input validation
