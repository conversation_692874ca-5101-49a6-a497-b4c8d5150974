"""
Refactored TeamLogic-AutoTask Application
Main entry point that orchestrates all modular components.
"""

import warnings
warnings.filterwarnings("ignore", message="You have an incompatible version of 'pyarrow' installed")

import streamlit as st
import json
import os
import pandas as pd
import plotly.express as px
from datetime import datetime, timedelta, date
from collections import Counter
from typing import List, Dict
import imaplib
import email
from email.header import decode_header
from email.utils import parsedate_to_datetime, parseaddr
import pytz
import re
import threading
import time
import schedule

# Import modular components
from config import *
from src.agents import IntakeClassificationAgent
from src.data import DataManager
from src.ui import apply_custom_css, create_sidebar, format_time_elapsed, format_date_display, get_duration_icon

# Email integration config (based on test.py)
EMAIL_ACCOUNT = '<EMAIL>'
EMAIL_PASSWORD = os.getenv('SUPPORT_EMAIL_PASSWORD')
IMAP_SERVER = 'imap.gmail.com'
FOLDER = 'inbox'
DEFAULT_TZ = 'Asia/Kolkata'
MAX_EMAILS = 20  # Increased slightly for 5-minute window
RECENT_MINUTES = 5  # Only process emails from last 5 minutes
DEFAULT_DUE_OFFSET_HOURS = 48
IST = pytz.timezone(DEFAULT_TZ)

# Global variables for automatic email processing
AUTO_EMAIL_PROCESSOR = None
EMAIL_PROCESSING_STATUS = {
    "is_running": False,
    "last_processed": None,
    "total_processed": 0,
    "error_count": 0,
    "recent_logs": []
}

# --- Authentication and Role-based Dashboard Switching ---
def authenticate_user(email, password, db_conn):
    """
    Authenticate user or technician by email and password from USER_DUMMY_DATA and TECHNICIAN_DUMMY_DATA.
    Returns (role, user_info) where role is 'user' or 'technician' or None.
    """
    # Check user table
    user_query = """
        SELECT USERID, USEREMAIL, NAME, PHONENUMBER, PASSWORD
        FROM TEST_DB.PUBLIC.USER_DUMMY_DATA
        WHERE USEREMAIL = %s
        LIMIT 1;
    """
    user_result = db_conn.execute_query(user_query, (email,))
    if user_result and user_result[0].get('PASSWORD') == password:
        return 'user', user_result[0]
    # Check technician table
    tech_query = """
        SELECT TECHNICIAN_ID as USERID, EMAIL as USEREMAIL, NAME, ROLE, PHONENUMBER, PASSWORD
        FROM TEST_DB.PUBLIC.TECHNICIAN_DUMMY_DATA
        WHERE EMAIL = %s
        LIMIT 1;
    """
    tech_result = db_conn.execute_query(tech_query, (email,))
    if tech_result and tech_result[0].get('PASSWORD') == password:
        return 'technician', tech_result[0]
    return None, None


def login_page(db_conn):
    st.title("🔐 Login to TeamLogic-AutoTask")
    st.markdown("Enter your email and password to access your dashboard.")
    with st.form("login_form"):
        email = st.text_input("Email", key="login_email")
        password = st.text_input("Password", type="password", key="login_password")
        submitted = st.form_submit_button("Login")
        if submitted:
            with st.spinner("Authenticating..."):
                role, user_info = authenticate_user(email, password, db_conn)
                if role:
                    st.session_state.authenticated = True
                    st.session_state.role = role
                    st.session_state.user_info = user_info
                    st.success(f"Logged in as {role.title()}: {user_info.get('NAME', user_info.get('USEREMAIL'))}")
                    st.experimental_rerun()
                else:
                    st.error("Invalid email or password. Please try again.")
    st.markdown("---")
    st.markdown("**Test Accounts:** See `auth.txt` for sample logins.")


def logout():
    for key in ["authenticated", "role", "user_info"]:
        if key in st.session_state:
            del st.session_state[key]
    st.experimental_rerun()


def get_user_tickets(db_conn, user_id):
    query = """
        SELECT * FROM TEST_DB.PUBLIC.TICKETS WHERE USERID = %s ORDER BY DUEDATETIME DESC;
    """
    return db_conn.execute_query(query, (user_id,))

def get_ticket_chat(db_conn, ticket_number):
    query = """
        SELECT SENDER, MESSAGE, TIMESTAMP FROM TEST_DB.PUBLIC.CHAT_MESSAGES WHERE TICKETNUMBER = %s ORDER BY TIMESTAMP ASC;
    """
    return db_conn.execute_query(query, (ticket_number,))

def send_chat_message(db_conn, ticket_number, sender, message):
    query = """
        INSERT INTO TEST_DB.PUBLIC.CHAT_MESSAGES (TICKETNUMBER, SENDER, MESSAGE, TIMESTAMP)
        VALUES (%s, %s, %s, CURRENT_TIMESTAMP());
    """
    db_conn.execute_query(query, (ticket_number, sender, message))

def get_technician_tickets(db_conn, technician_email):
    query = """
        SELECT * FROM TEST_DB.PUBLIC.TICKETS WHERE TECHNICIANEMAIL = %s ORDER BY DUEDATETIME DESC;
    """
    return db_conn.execute_query(query, (technician_email,))

def update_ticket_status_priority_note(db_conn, ticket_number, status, priority, note):
    query = """
        UPDATE TEST_DB.PUBLIC.TICKETS
        SET STATUS = %s, PRIORITY = %s, RESOLUTION = %s
        WHERE TICKETNUMBER = %s;
    """
    db_conn.execute_query(query, (status, priority, note, ticket_number))

def user_dashboard(agent, data_manager):
    st.title("👤 User Dashboard")
    st.button("Logout", on_click=logout, key="logout_btn_user")
    db_conn = agent.db_connection
    user_info = st.session_state.get("user_info", {})
    user_id = user_info.get("USERID")
    user_email = user_info.get("USEREMAIL")

    st.header("Raise a New Ticket")
    with st.form("raise_ticket_form"):
        ticket_title = st.text_input("Title")
        ticket_description = st.text_area("Description")
        due_date = st.date_input("Due Date")
        priority = st.selectbox("Priority", PRIORITY_OPTIONS)
        submitted = st.form_submit_button("Submit Ticket")
        if submitted:
            with st.spinner("Submitting ticket..."):
                result = agent.process_new_ticket(
                    ticket_name=user_info.get("NAME", user_email),
                    ticket_description=ticket_description,
                    ticket_title=ticket_title,
                    due_date=str(due_date),
                    priority_initial=priority,
                    user_email=user_email
                )
                if result:
                    st.success(f"Ticket {result.get('TICKETNUMBER', '')} submitted successfully!")
                else:
                    st.error("Failed to submit ticket.")

    st.header("My Tickets")
    status_filter = st.selectbox("Filter by Status", ["All", "Open", "In Progress", "Resolved", "Closed"])
    tickets = get_user_tickets(db_conn, user_id)
    if status_filter != "All":
        tickets = [t for t in tickets if t.get("STATUS", "Open") == status_filter]
    if not tickets:
        st.info("No tickets found.")
        return
    for ticket in tickets:
        with st.expander(f"{ticket.get('TITLE', 'No Title')} (Status: {ticket.get('STATUS', 'Open')})"):
            st.write(f"**Description:** {ticket.get('DESCRIPTION', '')}")
            st.write(f"**Due Date:** {ticket.get('DUEDATETIME', '')}")
            st.write(f"**Priority:** {ticket.get('PRIORITY', '')}")
            st.write(f"**Resolution:** {ticket.get('RESOLUTION', 'Not yet resolved')}")
            # Chat section
            st.subheader("Chat with Technician")
            chat_history = get_ticket_chat(db_conn, ticket.get("TICKETNUMBER"))
            for msg in chat_history:
                st.markdown(f"**{msg['SENDER']}:** {msg['MESSAGE']}<br/><small>{msg['TIMESTAMP']}</small>", unsafe_allow_html=True)
            chat_input = st.text_input(f"Type your message for {ticket.get('TICKETNUMBER')}", key=f"chat_{ticket.get('TICKETNUMBER')}")
            if st.button(f"Send", key=f"send_{ticket.get('TICKETNUMBER')}"):
                if chat_input.strip():
                    send_chat_message(db_conn, ticket.get("TICKETNUMBER"), user_email, chat_input.strip())
                    st.experimental_rerun()
                else:
                    st.warning("Message cannot be empty.")


def technician_dashboard(agent, data_manager):
    st.title("🛠️ Technician Dashboard")
    st.button("Logout", on_click=logout, key="logout_btn_tech")
    db_conn = agent.db_connection
    tech_info = st.session_state.get("user_info", {})
    technician_email = tech_info.get("USEREMAIL")

    st.header("Assigned Tickets")
    tickets = get_technician_tickets(db_conn, technician_email)
    if not tickets:
        st.info("No assigned tickets.")
        return
    for ticket in tickets:
        with st.expander(f"{ticket.get('TITLE', 'No Title')} (Status: {ticket.get('STATUS', 'Open')})"):
            st.write(f"**Description:** {ticket.get('DESCRIPTION', '')}")
            st.write(f"**Due Date:** {ticket.get('DUEDATETIME', '')}")
            st.write(f"**Priority:** {ticket.get('PRIORITY', '')}")
            st.write(f"**Resolution:** {ticket.get('RESOLUTION', 'Not yet resolved')}")
            # Update status/priority/note
            st.subheader("Update Ticket")
            new_status = st.selectbox("Status", ["Open", "In Progress", "Resolved", "Closed"], index=["Open", "In Progress", "Resolved", "Closed"].index(ticket.get("STATUS", "Open")), key=f"status_{ticket.get('TICKETNUMBER')}")
            new_priority = st.selectbox("Priority", PRIORITY_OPTIONS, index=PRIORITY_OPTIONS.index(ticket.get("PRIORITY", "Medium")), key=f"priority_{ticket.get('TICKETNUMBER')}")
            new_note = st.text_area("Resolution/Note", value=ticket.get("RESOLUTION", ""), key=f"note_{ticket.get('TICKETNUMBER')}")
            if st.button("Update Ticket", key=f"update_{ticket.get('TICKETNUMBER')}"):
                update_ticket_status_priority_note(db_conn, ticket.get("TICKETNUMBER"), new_status, new_priority, new_note)
                st.success("Ticket updated!")
                st.experimental_rerun()
            # Chat section
            st.subheader("Chat with User")
            chat_history = get_ticket_chat(db_conn, ticket.get("TICKETNUMBER"))
            for msg in chat_history:
                st.markdown(f"**{msg['SENDER']}:** {msg['MESSAGE']}<br/><small>{msg['TIMESTAMP']}</small>", unsafe_allow_html=True)
            chat_input = st.text_input(f"Type your message for {ticket.get('TICKETNUMBER')}", key=f"chat_tech_{ticket.get('TICKETNUMBER')}")
            if st.button(f"Send", key=f"send_tech_{ticket.get('TICKETNUMBER')}"):
                if chat_input.strip():
                    send_chat_message(db_conn, ticket.get("TICKETNUMBER"), technician_email, chat_input.strip())
                    st.experimental_rerun()
                else:
                    st.warning("Message cannot be empty.")


def main():
    st.set_page_config(
        page_title=PAGE_TITLE,
        layout=LAYOUT,
        page_icon=PAGE_ICON,
        initial_sidebar_state="expanded"
    )
    apply_custom_css()
    data_manager = DataManager(DATA_REF_FILE, KNOWLEDGEBASE_FILE)
    agent = get_agent(SF_ACCOUNT, SF_USER, SF_PASSWORD, SF_WAREHOUSE, SF_DATABASE, SF_SCHEMA, SF_ROLE, SF_PASSCODE, DATA_REF_FILE)
    db_conn = agent.db_connection if agent else None
    if not db_conn:
        st.error("Database connection failed. Cannot proceed.")
        return
    # Authentication logic
    if not st.session_state.get("authenticated"):
        login_page(db_conn)
        return
    # Role-based dashboard
    if st.session_state.get("role") == "user":
        user_dashboard(agent, data_manager)
    elif st.session_state.get("role") == "technician":
        technician_dashboard(agent, data_manager)
    else:
        st.error("Unknown role. Please log in again.")
        logout()

if __name__ == "__main__":
    main()